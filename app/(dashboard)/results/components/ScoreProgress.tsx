import React from "react";

export interface ScoreProgressProps {
  percentage: number; // 0-100
  stroke?: string;
  strokeWidth?: number;
  background?: string;
  diameter?: number;
  orientation?: "up" | "down";
  direction?: "left" | "right";
  showPercentValue?: boolean;
}

const ScoreProgress: React.FC<ScoreProgressProps> = ({
  percentage,
  stroke = "#A689FA",
  strokeWidth = 8,
  background = "#ECECEC",
  diameter = 80,
  orientation = "up",
  direction = "right",
  showPercentValue = true,
}) => {
  const coordinateForCircle = diameter / 2;
  const radius = (diameter - 2 * strokeWidth) / 2;
  const circumference = Math.PI * radius;

  // Clamp percentage between 0 and 100
  let percentageValue: number;
  if (percentage > 100) {
    percentageValue = 100;
  } else if (percentage < 0) {
    percentageValue = 0;
  } else {
    percentageValue = percentage;
  }
  const semiCirclePercentage = percentageValue * (circumference / 100);

  // Handle orientation and direction
  let rotation = undefined as string | undefined;
  if (orientation === "down") {
    if (direction === "left") {
      rotation = "rotate(180deg) rotateY(180deg)";
    } else {
      rotation = "rotate(180deg)";
    }
  } else {
    if (direction === "right") {
      rotation = "rotateY(180deg)";
    }
  }

  return (
    <div style={{ position: "relative", width: diameter, height: diameter / 2 }}>
      <svg
        width={diameter}
        height={diameter / 2}
        style={{ transform: rotation, overflow: "hidden", display: "block" }}
      >
        {/* Background arc */}
        <circle
          cx={coordinateForCircle}
          cy={coordinateForCircle}
          r={radius}
          fill="none"
          stroke={background}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          style={{ strokeDashoffset: circumference }}
        />
        {/* Foreground arc */}
        <circle
          cx={coordinateForCircle}
          cy={coordinateForCircle}
          r={radius}
          fill="none"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          style={{
            strokeDashoffset: semiCirclePercentage,
            transition:
              "stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s",
          }}
        />
      </svg>
      {showPercentValue && (
        <span
          style={{
            width: "100%",
            left: 0,
            textAlign: "center",
            bottom: orientation === "down" ? "auto" : 0,
            position: "absolute",
            fontSize: '12px',
            fontWeight: 800,
            color: stroke,
            fontFamily: 'Plus Jakarta Sans, sans-serif',
            lineHeight: 1,
            userSelect: "none",
            pointerEvents: "none",
          }}
        >
          {Number.isInteger(percentageValue) ? percentageValue : percentageValue.toFixed(1)}%
        </span>
      )}
    </div>
  );
};

export default ScoreProgress;
