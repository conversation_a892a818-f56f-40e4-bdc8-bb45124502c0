import React from 'react';
import { Box, Typography, Stack } from '@mui/material';
import styles from './OverallMetrics.module.scss';

interface Metric {
  name: string;
  value: number;
}

interface OverallMetricsProps {
  metrics: Metric[];
}

const OverallMetrics: React.FC<OverallMetricsProps> = ({ metrics }) => {
  return (
    <Box className={styles.metricsCard}>
      <Typography className={styles.metricsTitle}>Overall Metrics</Typography>
      <Stack spacing={2.5} mt={3}>
        {metrics.map((metric, idx) => (
          <Stack key={idx} direction="row" alignItems="center" justifyContent="space-between">
            <Typography className={styles.metricLabel}>{metric.name}</Typography>
            <Box className={styles.metricProgressWrap}>
              <Box className={styles.metricProgressBg}>
                <Box
                  className={styles.metricProgressBar}
                  sx={{ width: `${metric.value}%` }}
                >
                  {metric.value > 0 && (
                    <Box className={styles.metricProgressDot}>
                      <svg xmlns="http://www.w3.org/2000/svg" width="8" height="7" viewBox="0 0 8 7" fill="none">
                        <circle cx="3.93379" cy="3.66777" r="2.56" transform="rotate(90 3.93379 3.66777)" stroke="white" strokeWidth="1.28"/>
                      </svg>
                    </Box>
                  )}
                </Box>
              </Box>
              <Typography className={styles.metricPercent}>
                {Number.isInteger(metric.value) ? metric.value : metric.value.toFixed(1)}%
              </Typography>
            </Box>
          </Stack>
        ))}
      </Stack>
    </Box>
  );
};

export default OverallMetrics;