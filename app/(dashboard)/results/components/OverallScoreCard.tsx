import React from 'react';
import styles from './OverallScoreCard.module.scss';
import Image from 'next/image';

interface OverallScoreCardProps {
  score: number;
  onClick?: () => void;
}

const OverallScoreCard: React.FC<OverallScoreCardProps> = ({ score, onClick }) => (
  <div className={styles.overallScoreCard} onClick={onClick} tabIndex={0} role="button">
    <span className={styles.icon}>
      {/* Use Union-score.svg from public folder */}
      <Image src="/Union-score.svg" alt="Score Icon" width={15} height={18} />
    </span>
    <span className={styles.label}>Overall Score:</span>
    <span className={styles.value}>{Number.isInteger(score) ? score : score.toFixed(1)}%</span>
    <span className={styles.spacer} />
    <span className={styles.arrow}>
      {/* Right arrow SVG */}
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 6L15 12L9 18" stroke="#23272E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    </span>
  </div>
);

export default OverallScoreCard;