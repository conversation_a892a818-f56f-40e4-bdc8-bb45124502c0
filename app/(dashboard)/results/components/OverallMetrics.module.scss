.metricsCard {
  background: #fff;
  border-radius: 32px;
  padding: 24px;
  margin-bottom: 32px;
  height: 100%;
  box-shadow: none;
}

.metricsTitle {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px !important;
  font-weight: 700 !important;
  color: #242E2C;
  margin-bottom: 0;
  line-height: 1.1;
}

.metricLabel {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 14px !important;
  font-weight: 400;
  color: #414346;
  flex: 1;
  margin-right: 16px;
}

.metricProgressWrap {
  display: flex;
  align-items: center;
  width: 140px;
  gap: 12px;
}

.metricProgressBg {
  width: 100px;
  height: 12px;
  background: #F4EBFF;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  margin-right: 8px;
}

.metricProgressBar {
  height: 100%;
  background: #9E77ED;
  border-radius: 4px;
  position: relative;
  transition: width 0.3s;
  display: flex;
  align-items: center;
}

.metricProgressDot {
  position: absolute;
  margin-right: 6.4px;
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.metricPercent {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #9E77ED;
  min-width: 42px;
  text-align: right;
}