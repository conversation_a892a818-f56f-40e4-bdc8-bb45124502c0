import useMetricManagementAnalytics from "./useMetricManagementAnalytics";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";

export const useMetricsAnalytics = () => {
  const metricManagementAnalytics = useMetricManagementAnalytics();
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();

  const trackMetricCreated = (metricId: string) => {
    metricManagementAnalytics.trackMetricCreationButtonClicked("metrics_page", metricId);
  };

  const trackMetricEdited = (metricId: string) => {
    metricManagementAnalytics.trackMetricEditClicked(metricId, 0);
  };

  const trackMetricDeleted = (metricId: string) => {
    metricManagementAnalytics.trackMetricDeleteClicked(metricId, 0);
  };

  const trackApiRequestFailed = (operation: string, status: string, message: string) => {
    generalAnalyticsEvents.trackApiRequestFailed(operation, status, message);
  };

  return {
    trackMetricCreated,
    trackMetricEdited,
    trackMetricDeleted,
    trackApiRequestFailed,
  };
}; 