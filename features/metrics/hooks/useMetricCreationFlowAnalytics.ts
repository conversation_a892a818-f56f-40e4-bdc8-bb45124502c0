import { useMemo } from "react";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { usePathname } from "next/navigation";

const useMetricCreationFlowAnalytics = () => {
  const analytics = useRudderStackAnalytics();
  const { user } = useAuthStore((state) => state);
  const pathname = usePathname();

  return useMemo(() => {
    const track = (
      eventName: string,
      payload: Record<string, unknown> = {},
    ) => {
      if (!analytics) return;
      analytics.track(eventName, {
        user_id: user?.id,
        email: user?.email,
        domain: user?.email.split("@")[1],
        timestamp: new Date().toISOString(),
        app: "test.ai",
        category: "Metrics",
        page_name: pathname,
        ...payload,
      });
    };

    return {
      trackMetricCreationModalOpened: (modal_source: string) =>
        track("metric_creation_modal_opened", { modal_source }),
      trackMetricNameEntered: (metric_name: string) =>
        track("metric_name_entered", {
          metric_name,
          character_count: metric_name.length,
        }),
      trackMetricTypeDropdownOpened: (dropdown_open_count: number) =>
        track("metric_type_dropdown_opened", { dropdown_open_count }),
      trackMetricTypeSelected: (metric_type: string, selection_time: number) =>
        track("metric_type_selected", { metric_type, selection_time }),
      trackMetricDescriptionEntered: (description: string) => {
        const keywords = ["support", "context", "conversation", "intent"];
        const contains_keywords = keywords.some((keyword) =>
          description.toLowerCase().includes(keyword),
        );
        track("metric_description_entered", {
          character_count: description.length,
          contains_keywords,
        });
      },
      trackMetricCreationSubmitted: (
        form_completion_time: number,
        all_fields_filled: boolean,
      ) =>
        track("metric_creation_submitted", {
          form_completion_time,
          all_fields_filled,
        }),
      trackMetricCreationCompleted: (
        metric_id: string,
        metric_name: string,
        metric_type: string,
      ) =>
        track("metric_creation_completed", {
          metric_id,
          metric_name,
          metric_type,
        }),
      trackMetricCreationAbandoned: (
        time_spent: number,
        fields_filled: number,
        modal_close_method: string,
      ) =>
        track("metric_creation_abandoned", {
          time_spent,
          fields_filled,
          modal_close_method,
        }),
    };
  }, [analytics, user?.id]);
};

export default useMetricCreationFlowAnalytics;
