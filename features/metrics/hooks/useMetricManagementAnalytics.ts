import { useMemo } from "react";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { usePathname } from "next/navigation";

const useMetricManagementAnalytics = () => {
  const analytics = useRudderStackAnalytics();
  const { user } = useAuthStore((state) => state);
  const pathname = usePathname();

  return useMemo(() => {
    const track = (
      eventName: string,
      payload: Record<string, unknown> = {},
    ) => {
      if (!analytics) return;
      analytics.track(eventName, {
        user_id: user?.id,
        email: user?.email,
        domain: user?.email.split("@")[1],
        timestamp: new Date().toISOString(),
        app: "test.ai",
        category: "Metrics",
        page_name: pathname,
        ...payload,
      });
    };

    return {
      trackMetricCreationButtonClicked: (
        source_location: string,
        agent_id: string,
      ) =>
        track("metric_creation_button_clicked", { source_location, agent_id }),
      trackMetricListViewed: (agent_id: string, metrics_count: number) =>
        track("metric_list_viewed", { agent_id, metrics_count }),
      trackMetricExpanded: (metric_id: string, metric_name: string) =>
        track("metric_expanded", { metric_id, metric_name }),
      trackMetricCollapsed: (metric_id: string, time_spent_expanded: number) =>
        track("metric_collapsed", { metric_id, time_spent_expanded }),
      trackMetricActionMenuOpened: (metric_id: string, metric_name: string) =>
        track("metric_action_menu_opened", { metric_id, metric_name }),
      trackMetricEditClicked: (metric_id: string, metric_age: number) =>
        track("metric_edit_clicked", { metric_id, metric_age }),
      trackMetricDeleteClicked: (metric_id: string, metric_age: number) =>
        track("metric_delete_clicked", { metric_id, metric_age }),
      trackMetricDeleteConfirmed: (
        metric_id: string,
        confirmation_time: number,
      ) => track("metric_delete_confirmed", { metric_id, confirmation_time }),
      trackMetricDeleteCanceled: (metric_id: string, time_to_cancel: number) =>
        track("metric_delete_canceled", { metric_id, time_to_cancel }),
      trackMetricPaginationUsed: (
        page_number: number,
        direction: string,
        metrics_per_page: number,
      ) =>
        track("metric_pagination_used", {
          page_number,
          direction,
          metrics_per_page,
        }),
    };
  }, [analytics, user?.id]);
};

export default useMetricManagementAnalytics;
