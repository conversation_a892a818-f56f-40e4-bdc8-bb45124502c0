import { IAddNewMetricDto } from "@/app/api/types/metricDto";
import { useQueryClient } from "@tanstack/react-query";
import { IMetricDto } from "@/app/api/types/metricDto";
import { useMutation } from "@tanstack/react-query";
import { updateMetric } from "../services/metricService";

/**
 * Updates an existing metric for an agent
 * @param agentId - The ID of the agent to update the metric for
 * @param metricId - The ID of the metric to update
 * @param data - The data for the updated metric
 */
export function useUpdateMetricMutation(agentId: string, metricId: string) {
    const queryClient = useQueryClient();
    return useMutation<IMetricDto, Error, IAddNewMetricDto>({
        mutationFn: (data) => updateMetric(agentId, metricId, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['metrics', agentId] });
        },
    });
}