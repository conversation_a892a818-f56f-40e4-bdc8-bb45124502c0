import { IMetricDto } from "@/app/api/types/metricDto";
import { useQuery } from "@tanstack/react-query";
import { fetchMetrics } from "../services/metricService";

/**
 * Fetches all metrics for an agent
 * @param agentId - The ID of the agent to fetch metrics for
 * @returns A list of metrics for the agent
 */
export function useMetricsQuery(agentId?: string) {
    return useQuery<IMetricDto[], Error>({
        queryKey: ['metrics', agentId],
        queryFn: () => {
            if (!agentId) throw new Error('No agentId provided');
            return fetchMetrics(agentId);
        },
        enabled: !!agentId,
    });
}