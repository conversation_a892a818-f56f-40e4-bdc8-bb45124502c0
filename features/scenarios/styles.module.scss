@import '@/styles/variables.module';

.buttonsWrap {
  width: fit-content;
  align-self: flex-end;
  margin: auto 0 auto auto !important;
}

.idSelect {
  color: red !important;

  :global {

    .ant-select-selector {
      padding-left: 4px !important;
      color: $gray !important;
    }

    .ant-select-arrow {
      inset-inline-end: 0 !important;
    }

    .ant-select-clear {
      inset-inline-end: 21px !important;
    }

  }
}

button.runButton {
  width: 48px !important;
  height: 48px;
  padding: 12px;
  border-radius: 4px;
  font-size: 24px;
  color: $gray;

  :global {
    .anticon-loading {
      font-size: 24px !important;
    }
  }
}

.bx {
  font-size: 24px;
  cursor: pointer;
}

.descriptions {
  tr, td, th {
    border-color: transparent !important;
  }

  .descriptionsLabel {
    font-size: 14px;
    font-weight: 600;
    line-height: 21px;
    color: $black;
  }

  .descriptionsValue {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: $black;
    white-space: pre-wrap;
  }

  :global {
    .ant-descriptions-item-label {
      padding: 28px 24px 12px 56px !important;
    }

    .ant-descriptions-item-content {
      padding: 28px 24px 12px 24px !important;
    }
  }
}

.metrics {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}


.personality {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: $gray;
}

.link {
  cursor: pointer;
}

.ellipsisAnimation {
  &:after {
    overflow: hidden;
    display: inline-block;
    vertical-align: bottom;
    -webkit-animation: ellipsis steps(4,end) 900ms infinite;
    animation: ellipsis steps(4,end) 900ms infinite;
    content: "\2026"; /* ascii code for the ellipsis character */
    width: 0;
  }
}

@keyframes ellipsis {
  to {
    width: 1.25em;
  }
}

@-webkit-keyframes ellipsis {
  to {
    width: 1.25em;
  }
}

.dropdown {
  width: 224px;

  .dropdownTitle {
    color: $black;
  }

  .dropdownItem {
    padding: 8px !important;

    button {
      padding: 0;
    }
  }
}

.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  width: 100%;
}

@media (max-width: 900px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 600px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 14px;
  }
}

.addButton {
  background-color: #7F56D9;
  border-radius: 24px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #fff;
  box-shadow: none;
  height: 64px;
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
  margin-top: 24px;
  margin-bottom: 0;
  text-transform: none;
  &:hover, &:focus {
    background-color: #6941C6;
  }
}
