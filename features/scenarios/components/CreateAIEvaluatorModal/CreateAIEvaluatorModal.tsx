// import {
//   Box,
//   <PERSON>ton,
//   Dialog,
//   DialogTitle,
//   DialogContent,
//   DialogActions,
//   IconButton,
//   FormControl,
//   MenuItem,
//   Select,
//   TextField,
//   Tooltip,
//   Typography,
//   CircularProgress,
// } from "@mui/material";
// import CloseIcon from "@mui/icons-material/Close";
// import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
// import { useGeneralStore } from "@/providers/general-store-provider";
// import { useAuthStore } from "@/stores/auth-store";
// import { useNotification } from "@/context/NotificationContext/NotificationContext";
// import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
// import { IErrorFields } from "@/types/validateError";
// import { IScenario } from "@/types/scenario";
// import { EMetricType, IMetric } from "@/types/metric";
// import { IAddNewScenarioDto, IEditScenarioDto, IScenarioDto } from "@/app/api/types/scenarioDto";
// import Image from "next/image";
// import { useEffect, useState } from "react";
// import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
// import { usePathname } from "next/navigation";
// import { useOnboardingStore } from '@/stores/onboarding-store';

// interface ICreateAIEvaluatorModalProps {
//   onCancel: () => void;
//   isModalOpen: boolean;
//   initialData: IScenario | null;
//   onSuccess?: (scenario: IScenario) => void;
// }

// interface MetricResponse {
//   id: number;
//   type: EMetricType;
//   prompt: string;
//   name: string;
// }

// // interface ICreateEditMetricForm {
// //   scenarioName: string;
// //   personality: string;
// //   metrics: string[];
// //   instructions: string;
// //   prompt: string;
// // }

// const useCreateAIEvaluatorAnalytics = () => {
//   const analytics = useRudderStackAnalytics();
//   const { user } = useAuthStore((state) => state);
//   const pathname = usePathname();

//   return {
//     trackTestCreationStarted: () => {
//       if (!analytics) return;
//       analytics.track("Test_Creation_Started", {
//         user_id: user?.id,
//         userId: user?.id,
//         email: user?.email,
//         domain: user?.email.split("@")[1],
//         timestamp: new Date().toISOString(),
//         app: "test.ai",
//         category: "Simulation",
//         page_name: pathname,
//         entry_point: "manual",
//       });
//     },
//     trackTestCreationAbandoned: (error_message?: string) => {
//       if (!analytics) return;
//       analytics.track("Test_Creation_Abandoned", {
//         user_id: user?.id,
//         userId: user?.id,
//         email: user?.email,
//         domain: user?.email.split("@")[1],
//         timestamp: new Date().toISOString(),
//         app: "test.ai",
//         category: "Simulation",
//         page_name: pathname,
//         error_message,
//       });
//     },
//     trackUpdateScenario: () => {
//       if (!analytics) return;
//       analytics.track("Update Scenario", {
//         user_id: user?.id,
//         userId: user?.id,
//         email: user?.email,
//         domain: user?.email.split("@")[1],
//         timestamp: new Date().toISOString(),
//         app: "test.ai",
//         category: "Simulation",
//         page_name: pathname,
//       });
//     },
//     trackTestCreationCompleted: (responseBody: IScenarioDto) => {
//       if (!analytics) return;
//       analytics.track("Test_Creation_Completed", {
//         user_id: user?.id,
//         userId: user?.id,
//         email: user?.email,
//         domain: user?.email.split("@")[1],
//         timestamp: new Date().toISOString(),
//         app: "test.ai",
//         category: "Simulation",
//         page_name: pathname,
//         test_type: `Name: ${responseBody.name}, personality: ${responseBody.personality}, metrics: ${responseBody.metrics.map(
//           (metric) => metric.name,
//         )}`,
//       });
//     },
//     trackCreateScenario: () => {
//       if (!analytics) return;
//       analytics.track("Create Scenario", {
//         user_id: user?.id,
//         userId: user?.id,
//         email: user?.email,
//         domain: user?.email.split("@")[1],
//         timestamp: new Date().toISOString(),
//         app: "test.ai",
//         category: "Simulation",
//         page_name: pathname,
//       });
//     },
//   };
// };

// const CreateAIEvaluatorModal = ({
//   onCancel,
//   isModalOpen,
//   initialData,
//   onSuccess,
// }: ICreateAIEvaluatorModalProps) => {
//   const generalAnalyticsEvents = useGeneralAnalyticsEvents();
//   const evaluatorAnalytics = useCreateAIEvaluatorAnalytics();
//   const { user } = useAuthStore((state) => state);
//   const notify = useNotification();
//   const [loading, setLoading] = useState(false);
//   const [submittable, setSubmittable] = useState<boolean>(false);
//   const [isCreated, setIsCreated] = useState<boolean>(false);
//   const [modalOpenedTime, setModalOpenedTime] = useState<number | null>(null);
//   const { addScenario, editScenario, currentAgentId, setMetrics } = useGeneralStore((state) => state);
//   const [, setValidationErrors] = useState<IErrorFields[]>([]);
//   const [metricsLoading, setMetricsLoading] = useState(true);
//   const [availableMetrics, setAvailableMetrics] = useState<IMetric[]>([]);
//   const { setStepComplete, nextStep } = useOnboardingStore();

//   // Form state
//   const [scenarioName, setScenarioName] = useState<string>(initialData?.title || "");
//   const [personality, setPersonality] = useState<string>(initialData?.personality || "");
//   const [selectedMetrics, setSelectedMetrics] = useState<string[]>(initialData?.metrics.map(m => m.id) || []);
//   const [instructions, setInstructions] = useState<string>(initialData?.instruction || "");

//   // Track modal open time and analytics
//   useEffect(() => {
//     if (isModalOpen) {
//       setModalOpenedTime(Date.now());
//       if (!initialData) {
//         evaluatorAnalytics.trackTestCreationStarted();
//       }
//     }

//     return () => {
//       if (!initialData && !isCreated) {
//         evaluatorAnalytics.trackTestCreationAbandoned();
//       }
//     };
//   }, [isModalOpen, initialData, isCreated]);

//   // Fetch metrics when modal opens
//   useEffect(() => {
//     const fetchMetrics = async () => {
//       if (!isModalOpen || !currentAgentId) return;
      
//       setMetricsLoading(true);
//       try {
//         const response = await fetch(`/api/agents/${currentAgentId}/metrics`, {
//           method: "GET",
//           headers: {
//             "Content-Type": "application/json",
//             Authorization: `Bearer ${user?.token || ""}`,
//           },
//         });

//         if (!response.ok) {
//           console.error(`HTTP error! status: ${response.status}`);
//           notify.error({
//             message: "Failed to get metrics",
//             description: `HTTP error! status: ${response.status}`,
//           });
//           generalAnalyticsEvents.trackApiRequestFailed(
//             "Get Metrics",
//             String(response.status),
//             `Failed to get metrics`,
//           );
//           return;
//         }

//         const responseBody = await response.json();
//         const metrics = responseBody.map((metric: MetricResponse) => ({
//           id: String(metric.id),
//           metricType: metric.type as EMetricType,
//           metricPrompt: metric.prompt,
//           name: metric.name,
//         }));
        
//         setAvailableMetrics(metrics);
//         setMetrics(metrics);
//       } catch (error) {
//         console.error("Error fetching metrics:", error);
//         notify.error({
//           message: "Unexpected Error",
//           description: "Failed to load metrics.",
//         });
//         generalAnalyticsEvents.trackApiRequestFailed(
//           "Get Metrics",
//           "500",
//           `Failed to load metrics`,
//         );
//       } finally {
//         setMetricsLoading(false);
//       }
//     };

//     fetchMetrics();
//   }, [isModalOpen, currentAgentId, user?.token]);

//   // Update form state when initialData changes
//   useEffect(() => {
//     if (initialData) {
//       setScenarioName(initialData.title);
//       setPersonality(initialData.personality);
//       setSelectedMetrics(initialData.metrics.map(m => m.id));
//       setInstructions(initialData.instruction || "");
//       setSubmittable(true);
//     }
//   }, [initialData]);

//   // Validate form
//   useEffect(() => {
//     if (initialData) {
//       setSubmittable(true);
//       return;
//     }

//     if (scenarioName.trim() && personality && selectedMetrics.length > 0) {
//       setSubmittable(true);
//     } else {
//       setSubmittable(false);
//     }
//   }, [scenarioName, personality, selectedMetrics, initialData]);

//   const resetForm = () => {
//     setScenarioName("");
//     setPersonality("");
//     setSelectedMetrics([]);
//     setInstructions("");
//     setValidationErrors([]);
//     setSubmittable(false);
//   };

//   const handleSubmit = async () => {
//     try {
//       setLoading(true);
      
//       if (initialData) {
//         // Update existing scenario
//         evaluatorAnalytics.trackUpdateScenario();
//         const response = await fetch(`/api/agents/${currentAgentId}/scenarios/${initialData.id}`, {
//           method: "PUT",
//           headers: {
//             "Content-Type": "application/json",
//             Authorization: `Bearer ${user?.token || ""}`,
//           },
//           body: JSON.stringify({
//             name: initialData.title,
//             instruction: instructions,
//             personality: personality,
//             id: Number(initialData.id),
//             prompt: initialData.prompt,
//             metrics: selectedMetrics,
//           } as IEditScenarioDto),
//         });

//         if (!response.ok) {
//           console.error(`HTTP error! status: ${response.status}`);
//           notify.error({
//             message: "Failed to update scenario",
//             description: `HTTP error! status: ${response.status}`,
//           });
//           generalAnalyticsEvents.trackApiRequestFailed(
//             "Update Scenario",
//             String(response.status),
//             `Failed to update scenario`,
//           );
//           return;
//         }

//         const responseBody: IScenarioDto = await response.json();
//         const updatedScenario = {
//           id: String(responseBody.id),
//           scenarioName: responseBody.name,
//           prompt: responseBody.prompt,
//           personality: responseBody.personality,
//           instructions: responseBody.instruction,
//           metrics: responseBody.metrics.map((metric) => ({
//             id: String(metric.id),
//             metricType: metric.type as EMetricType,
//             metricPrompt: metric.prompt,
//             name: metric.name,
//           })),
//         };

//         editScenario(updatedScenario);
//         evaluatorAnalytics.trackTestCreationCompleted(responseBody);
//         onSuccess?.(updatedScenario);
//       } else {
//         // Create new scenario
//         evaluatorAnalytics.trackCreateScenario();
//         const response = await fetch(`/api/agents/${currentAgentId}/scenarios`, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//             Authorization: `Bearer ${user?.token || ""}`,
//           },
//           body: JSON.stringify({
//             name: scenarioName,
//             instruction: instructions,
//             personality: personality,
//             metrics: selectedMetrics.map((metric) => Number(metric)),
//           } as IAddNewScenarioDto),
//         });

//         if (!response.ok) {
//           console.error(`HTTP error! status: ${response.status}`);
//           notify.error({
//             message: "Failed to add scenario",
//             description: `HTTP error! status: ${response.status}`,
//           });
//           evaluatorAnalytics.trackTestCreationAbandoned(
//             `HTTP error! status: ${response.status}`,
//           );
//           generalAnalyticsEvents.trackApiRequestFailed(
//             "Create Scenario",
//             String(response.status),
//             `Failed to add scenario`,
//           );
//           return;
//         }

//         const responseBody: IScenarioDto = await response.json();
//         const newScenario = {
//           id: String(responseBody.id),
//           scenarioName: responseBody.name,
//           prompt: responseBody.prompt,
//           personality: responseBody.personality,
//           instructions: responseBody.instruction,
//           metrics: responseBody.metrics.map((metric) => ({
//             id: String(metric.id),
//             metricType: metric.type as EMetricType,
//             metricPrompt: metric.prompt,
//             name: metric.name,
//           })),
//         };

//         // Mark second step as complete and move to next step
//         setStepComplete('createScenario');
//         nextStep();

//         addScenario(newScenario);
//         setIsCreated(true);
//         evaluatorAnalytics.trackTestCreationCompleted(responseBody);
//         onSuccess?.(newScenario);
//       }

//       resetForm();
//     } catch (error) {
//       console.error("Error adding/updating scenario:", error);
//       notify.error({
//         message: "Unexpected Error",
//         description: error instanceof Error ? error.message : `Failed to ${initialData ? "update" : "add"} scenario.`,
//       });
//       generalAnalyticsEvents.trackApiRequestFailed(
//         initialData ? "Update Scenario" : "Create Scenario",
//         "500",
//         error instanceof Error ? error.message : `Failed to ${initialData ? "update" : "add"} scenario.`,
//       );
//     } finally {
//       setLoading(false);
//     }

//     onCancel();
//   };

//   const handleCancel = () => {
//     const timeSpent = modalOpenedTime ? Date.now() - modalOpenedTime : 0;
//     const fieldsFilled = [scenarioName, personality, selectedMetrics, instructions].filter(Boolean).length;
//     generalAnalyticsEvents.trackModalCloseButtonClicked(
//       "CreateAIEvaluatorModal",
//       timeSpent,
//       `${fieldsFilled}/4`,
//     );
//     onCancel();
//   };

//   return (
//     <Dialog
//       open={isModalOpen}
//       onClose={handleCancel}
//       maxWidth="sm"
//       fullWidth
//       PaperProps={{
//         sx: { borderRadius: "12px", width: 565, backgroundColor: "#FFFFFF" },
//         "&.MuiDialogTitle-root": { borderBottom: "none" },
//       }}
//     >
//       <DialogTitle
//         sx={{
//           display: "flex",
//           justifyContent: "space-between",
//           p: 2,
//           borderBottom: "none",
//           "&.MuiDialogTitle-root": { borderBottom: "none" },
//         }}
//       >
//         <Box display="flex" alignItems="center" gap={1}>
//           <Box
//             sx={{
//               backgroundColor: "#ffff",
//               borderRadius: "15px",
//               display: "flex",
//               alignItems: "center",
//               justifyContent: "center",
//               padding: "12px",
//               boxShadow: "0px 0px 4.89px rgba(159, 159, 159, 0.25)",
//               transition: "all 0.3s ease",
//             }}
//           >
//             <Image
//               src={"/atomic-power.svg"}
//               alt={initialData ? "Update AI Evaluator" : "Create AI Evaluator"}
//               width={25}
//               height={25}
//             />
//           </Box>
//           <Typography
//             variant="h5"
//             sx={{ fontWeight: 700, fontFamily: "Plus Jakarta Sans" }}
//           >
//             {initialData ? "Update AI Evaluator" : "Create AI Evaluator"}
//           </Typography>
//         </Box>
//         <Box>
//           <IconButton
//             aria-label="close"
//             onClick={handleCancel}
//             size="small"
//             sx={{ color: "text.secondary" }}
//           >
//             <CloseIcon />
//           </IconButton>
//         </Box>
//       </DialogTitle>

//       <DialogContent>
//         <Box component="form" sx={{ mt: 1 }}>
//           {/* AI Evaluator Name */}
//           <FormControl fullWidth sx={{ mb: 2 }}>
//             <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
//               <Typography
//                 variant="subtitle1"
//                 sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
//               >
//                 AI Evaluator Name*
//               </Typography>
//             </Box>
//             <TextField
//               id="scenario-name"
//               required
//               value={scenarioName}
//               placeholder="e.g., Activation Bot"
//               autoComplete="off"
//               onChange={(e) => setScenarioName(e.target.value)}
//               sx={{
//                 fontFamily: "Plus Jakarta Sans",
//                 "& .MuiOutlinedInput-root": {
//                   borderRadius: "12px",
//                   "&:hover .MuiOutlinedInput-notchedOutline": {
//                     borderColor: "#7F56D9",
//                   },
//                   "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
//                     borderColor: "#7F56D9",
//                   },
//                 },
//               }}
//             />
//           </FormControl>

//           {/* Instructions */}
//           <FormControl fullWidth sx={{ mb: 2 }}>
//             <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
//               <Typography
//                 variant="subtitle1"
//                 sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
//               >
//                 Instructions
//               </Typography>
//             </Box>
//             <TextField
//               id="instructions"
//               multiline
//               rows={4}
//               value={instructions}
//               placeholder="Enter any additional instructions for the LLM to follow while generating scenarios"
//               onChange={(e) => setInstructions(e.target.value)}
//               sx={{
//                 fontFamily: "Plus Jakarta Sans",
//                 "& .MuiOutlinedInput-root": {
//                   borderRadius: "12px",
//                   "&:hover .MuiOutlinedInput-notchedOutline": {
//                     borderColor: "#7F56D9",
//                   },
//                   "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
//                     borderColor: "#7F56D9",
//                   },
//                 },
//               }}
//             />
//           </FormControl>

//           {/* Personality */}
//           <FormControl fullWidth sx={{ mb: 2 }}>
//             <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
//               <Typography
//                 variant="subtitle1"
//                 sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
//               >
//                 Personality*
//               </Typography>
//               <Tooltip
//                 sx={{ fontFamily: "Plus Jakarta Sans" }}
//                 title="This is the tone of the conversation that the AI will use"
//                 placement="right"
//                 onOpen={() =>
//                   generalAnalyticsEvents.trackHelpIconClicked(
//                     "CreateAIEvaluatorModal",
//                     "Personality"
//                   )
//                 }
//               >
//                 <InfoOutlinedIcon fontSize="small" sx={{ ml: 1 }} />
//               </Tooltip>
//             </Box>
//             <Select
//               id="personality"
//               value={personality}
//               onChange={(e) => setPersonality(e.target.value)}
//               displayEmpty
//               required
//               sx={{
//                 fontFamily: "Plus Jakarta Sans",
//                 borderRadius: "12px",
//                 "&:hover .MuiOutlinedInput-notchedOutline": {
//                   borderColor: "#7F56D9",
//                 },
//                 "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
//                   borderColor: "#7F56D9",
//                 },
//               }}
//             >
//               <MenuItem value="" disabled>
//                 Select personality
//               </MenuItem>
//               <MenuItem value="American Male">American Male</MenuItem>
//             </Select>
//           </FormControl>

//           {/* Metrics */}
//           <FormControl fullWidth sx={{ mb: 2 }}>
//             <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
//               <Typography
//                 variant="subtitle1"
//                 sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
//               >
//                 Metrics*
//               </Typography>
//               <Tooltip
//                 sx={{ fontFamily: "Plus Jakarta Sans" }}
//                 title="Metrics help you measure AI performance — track accuracy, response time, and key factors."
//                 placement="right"
//                 onOpen={() =>
//                   generalAnalyticsEvents.trackHelpIconClicked(
//                     "CreateAIEvaluatorModal",
//                     "Metrics"
//                   )
//                 }
//               >
//                 <InfoOutlinedIcon fontSize="small" sx={{ ml: 1 }} />
//               </Tooltip>
//             </Box>
//             {metricsLoading ? (
//               <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
//                 <CircularProgress size={24} />
//               </Box>
//             ) : (
//               <Select
//                 id="metrics"
//                 multiple
//                 value={selectedMetrics}
//                 onChange={(e) => setSelectedMetrics(e.target.value as string[])}
//                 displayEmpty
//                 required
//                 sx={{
//                   fontFamily: "Plus Jakarta Sans",
//                   borderRadius: "12px",
//                   "&:hover .MuiOutlinedInput-notchedOutline": {
//                     borderColor: "#7F56D9",
//                   },
//                   "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
//                     borderColor: "#7F56D9",
//                   },
//                 }}
//               >
//                 {availableMetrics.map((metric: IMetric) => (
//                   <MenuItem key={metric.id} value={metric.id}>
//                     {metric.name}
//                   </MenuItem>
//                 ))}
//               </Select>
//             )}
//           </FormControl>
//         </Box>
//       </DialogContent>

//       <DialogActions sx={{ px: 3, py: 2, borderTop: "none" }}>
//         <Button
//           fullWidth
//           size="large"
//           onClick={handleSubmit}
//           variant="contained"
//           color="primary"
//           sx={{
//             backgroundColor: "#7F56D9",
//             fontFamily: "Plus Jakarta Sans",
//             textTransform: "capitalize",
//             borderRadius: "12px",
//             height: "50px",
//           }}
//           disabled={!submittable || loading}
//         >
//           {loading ? "Processing..." : initialData ? "Update" : "Create"}
//         </Button>
//       </DialogActions>
//     </Dialog>
//   );
// };

// export default CreateAIEvaluatorModal;
