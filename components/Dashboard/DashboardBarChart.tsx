"use client";
import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import {
  BarC<PERSON>,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from 'recharts';

interface ChartDataPoint {
  month: string;
  year: number;
  runs?: number;
  total_latency_ms?: number;
}

interface DashboardBarChartProps {
  data: ChartDataPoint[];
  title?: string;
  dataKey?: string;
  barColor?: string;
  xAxisTickFormatter?: (month: string) => string;
}

// Custom tooltip component to match Figma design
const CustomBarChartTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload || !payload.length) return null;

  const data = payload[0].payload;
  const value = payload[0].value;
  const name = payload[0].name;

  return (
    <Box
      sx={{
        p: 2,
        borderRadius: '11px',
        bgcolor: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(5px)',
        border: '1px solid #F5F5F5',
        boxShadow: 'none',
        width: '160px',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Typography
          sx={{
            fontSize: '12px',
            fontWeight: 500,
            fontFamily: 'Inter',
            color: '#414346',
            width: '100%',
            textAlign: 'center',
          }}
        >
          {label} {data.year}
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: '4px',
              height: '20px',
              bgcolor: '#9E77ED',
              borderRadius: '20px',
            }}
          />
          <Typography
            sx={{
              fontSize: '12px',
              fontWeight: 400,
              fontFamily: 'Inter',
              color: '#414346',
            }}
          >
            {name}
          </Typography>
        </Box>
        <Typography
          sx={{
            fontSize: '12px',
            fontWeight: 500,
            fontFamily: 'Inter',
            color: '#414346',
          }}
        >
          {value} ms
        </Typography>
      </Box>
    </Box>
  );
};

// Define the bar chart in a reusable component
const DashboardBarChart: React.FC<DashboardBarChartProps> = ({
  data,
  title = 'Overall Latency',
  dataKey = 'total_latency_ms',
  barColor = '#9E77ED',
  xAxisTickFormatter,
}) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const handleMouseEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  const handleMouseLeave = () => {
    setActiveIndex(null);
  };

  // No default active bar - bars will only be active on hover

  // Mock data for testing - this adds more data points if needed
  const mockData = React.useMemo(() => {
    if (data.length < 7) {
      const months = ['Jan', 'Mar', 'May', 'Jul', 'Sep', 'Nov', 'Dec'];
      const mockValues = [36, 44, 28, 36, 28, 40, 36];

      return months.map((month, index) => ({
        month,
        year: 2025,
        total_latency_ms: mockValues[index]
      }));
    }
    return data;
  }, [data]);

  return (
    <Box
      sx={{
        p: 4,
        borderRadius: '32px',
        backgroundColor: '#FFFFFF',
        border: '1px solid #E5E6E6',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        height: '100%',
        width: '100%',
      }}
    >
      {/* Title / Section Heading */}
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography
          sx={{
            fontSize: '16px',
            fontWeight: 'bold',
            fontFamily: 'Plus Jakarta Sans',
            color: '#242E2C',
            lineHeight: 'none',
          }}
        >
          {title}
        </Typography>
      </Box>

      <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={mockData}
            margin={{ top: 25, right: 20, bottom: 5, left: 20 }}
            barGap={0}
            barCategoryGap="30%"
            onMouseLeave={handleMouseLeave}
          >
            {/* Grid lines */}
            <CartesianGrid
              horizontal={true}
              vertical={false}
              stroke="#E6E6E6"
              strokeWidth={1}
              strokeOpacity={0.7}
            />

            {/* X-axis for months */}
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              stroke="#414346"
              tick={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
                fontSize: 12,
                letterSpacing: 0,
                fill: '#414346',
              }}
              tickFormatter={xAxisTickFormatter}
              dy={8}
              padding={{ left: 10, right: 10 }}
            />

            {/* Y-axis for values */}
            <YAxis
              tickLine={false}
              axisLine={false}
              stroke="#414346"
              tick={{
                fontFamily: 'Plus Jakarta Sans',
                fontSize: 12,
                fontWeight: 500,
                fill: '#414346',
              }}
              tickCount={5}
              domain={[0, 'dataMax']}
              allowDecimals={false}
              padding={{ top: 10 }}
            />

            {/* Custom tooltip */}
            <Tooltip
              cursor={{ fill: 'transparent' }}
              contentStyle={{
                borderRadius: 8,
                border: '1px solid #E0E0E0',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                fontFamily: 'Plus Jakarta Sans'
              }}
              labelStyle={{
                fontWeight: 'bold',
                color: '#333',
                fontFamily: 'Plus Jakarta Sans'
              }}
              formatter={(value: number, name: string) => [`${value}`, name]}
              labelFormatter={(label: string, payload: any[]) => {
                const item = payload && payload[0] ? payload[0].payload as ChartDataPoint : null;
                return item ? `${label} ${item.year}` : label;
              }}
            />

            {/* Bar Group */}
            <Bar
              dataKey={dataKey}
              name={title}
              radius={[6, 6, 0, 0]}
              barSize={24}
              fill={barColor}
              onMouseEnter={handleMouseEnter}
              shape={(props: any) => {
                const { x, y, width, height, index } = props;
                // Only highlight bars when they are hovered
                const isActive = index === activeIndex;
                const fill = isActive ? barColor : '#F6F6F6';

                return (
                  <rect
                    x={x}
                    y={y}
                    width={width}
                    height={height}
                    fill={fill}
                    rx={0}
                    ry={0}
                  />
                );
              }}
            />
          </BarChart>
        </ResponsiveContainer>

        {/* Removed fixed tooltip - tooltip will only appear on hover */}
      </Box>
    </Box>
  );
};

export default DashboardBarChart;
